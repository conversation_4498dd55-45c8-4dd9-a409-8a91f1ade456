namespace Model
{
    // Generic API Response wrapper
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public object? Errors { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public static ApiResponse<T> SuccessResult(T data, string message = "Success")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Message = message,
                Data = data
            };
        }

        public static ApiResponse<T> ErrorResult(string message, object? errors = null)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Errors = errors
            };
        }
    }

    // Paginated response
    public class PaginatedResponse<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }

        public PaginatedResponse(List<T> items, int totalCount, int page, int pageSize)
        {
            Items = items;
            TotalCount = totalCount;
            Page = page;
            PageSize = pageSize;
            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            HasNextPage = page < TotalPages;
            HasPreviousPage = page > 1;
        }
    }

    // Search and filter metadata
    public class SearchMetadata
    {
        public string? SearchTerm { get; set; }
        public Dictionary<string, object> AppliedFilters { get; set; } = new Dictionary<string, object>();
        public string? SortBy { get; set; }
        public string? SortOrder { get; set; }
        public int ResultCount { get; set; }
        public double SearchDurationMs { get; set; }
    }

    // Complete search response with metadata
    public class SearchResponse<T> : PaginatedResponse<T>
    {
        public SearchMetadata Metadata { get; set; }

        public SearchResponse(List<T> items, int totalCount, int page, int pageSize, SearchMetadata metadata)
            : base(items, totalCount, page, pageSize)
        {
            Metadata = metadata;
        }
    }
}
