# Handbags API Documentation

## Overview
This API provides comprehensive functionality for managing handbags with advanced search, pagination, sorting, and standardized response formats.

## Response Format
All API responses follow a standardized format:

```json
{
  "success": true,
  "message": "Success message",
  "data": { /* actual data */ },
  "errors": null,
  "timestamp": "2025-01-19T10:30:00Z"
}
```

## Authentication
All endpoints require JWT authentication with appropriate roles:
- `administrator`: Full access
- `moderator`: Full access except some admin functions
- `developer`: Read access
- `member`: Read access

## Endpoints

### 1. Get All Handbags (Paginated)
**GET** `/api/handbags`

**Query Parameters:**
- `page` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `sortBy` (string, optional): Field to sort by
- `sortOrder` (string, default: "asc"): Sort direction ("asc" or "desc")

**Example:**
```
GET /api/handbags?page=1&pageSize=20&sortBy=price&sortOrder=desc
```

**Response:**
```json
{
  "success": true,
  "message": "Handbags retrieved successfully",
  "data": {
    "items": [...],
    "totalCount": 150,
    "page": 1,
    "pageSize": 20,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

### 2. Get Handbag by ID
**GET** `/api/handbags/{id}`

**Response:**
```json
{
  "success": true,
  "message": "Handbag retrieved successfully",
  "data": {
    "handbagId": 1,
    "modelName": "Luxury Bag",
    "material": "Leather",
    "color": "Black",
    "price": 299.99,
    "stock": 15,
    "releaseDate": "2024-01-15",
    "brandName": "Gucci",
    "country": "Italy",
    "foundedYear": 1921,
    "website": "https://gucci.com"
  }
}
```

### 3. Advanced Search
**GET** `/api/handbags/search`

**Query Parameters:**
- All pagination parameters (page, pageSize, sortBy, sortOrder)
- `modelName` (string): Partial match on model name
- `material` (string): Partial match on material
- `color` (string): Partial match on color
- `brandName` (string): Partial match on brand name
- `minPrice` (decimal): Minimum price filter
- `maxPrice` (decimal): Maximum price filter
- `minStock` (int): Minimum stock filter
- `maxStock` (int): Maximum stock filter
- `fromDate` (DateOnly): From release date
- `toDate` (DateOnly): To release date

**Example:**
```
GET /api/handbags/search?modelName=luxury&minPrice=100&maxPrice=1000&brandName=gucci&page=1&pageSize=10&sortBy=price&sortOrder=asc
```

**Response:**
```json
{
  "success": true,
  "message": "Search completed successfully",
  "data": {
    "items": [...],
    "totalCount": 25,
    "page": 1,
    "pageSize": 10,
    "totalPages": 3,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "metadata": {
      "appliedFilters": {
        "ModelName": "luxury",
        "MinPrice": 100,
        "MaxPrice": 1000,
        "BrandName": "gucci"
      },
      "sortBy": "price",
      "sortOrder": "asc",
      "resultCount": 25,
      "searchDurationMs": 45.2
    }
  }
}
```

### 4. Grouped Search
**GET** `/api/handbags/search/grouped`

Same parameters as advanced search, but results are grouped by brand.

**Response:**
```json
{
  "success": true,
  "message": "Grouped search completed successfully",
  "data": {
    "groups": [
      {
        "brandName": "Gucci",
        "handbags": [...]
      },
      {
        "brandName": "Louis Vuitton", 
        "handbags": [...]
      }
    ],
    "totalCount": 25,
    "page": 1,
    "pageSize": 10,
    "totalPages": 3,
    "metadata": {...}
  }
}
```

### 5. Get Search Options
**GET** `/api/handbags/search/options`

Returns available search and sort options.

### 6. Create Handbag
**POST** `/api/handbags`
**Roles:** administrator, moderator

**Request Body:**
```json
{
  "modelName": "New Luxury Bag",
  "material": "Leather",
  "price": 299.99,
  "stock": 10,
  "brandId": 1
}
```

### 7. Update Handbag
**PUT** `/api/handbags/{id}`
**Roles:** administrator, moderator

**Request Body:**
```json
{
  "modelName": "Updated Bag Name",
  "material": "Premium Leather",
  "color": "Brown",
  "price": 349.99,
  "stock": 8,
  "brandId": 1
}
```

### 8. Delete Handbag
**DELETE** `/api/handbags/{id}`
**Roles:** administrator, moderator

## Sortable Fields
- `handbagid`
- `modelname`
- `material`
- `color`
- `price`
- `stock`
- `releasedate`
- `brandname`
- `country`
- `foundedyear`

## Error Handling
All errors follow the standard response format:

```json
{
  "success": false,
  "message": "Error description",
  "data": null,
  "errors": "Detailed error information",
  "timestamp": "2025-01-19T10:30:00Z"
}
```

## Performance Features
- Efficient database queries with proper indexing
- Search duration tracking
- Pagination to handle large datasets
- Optimized sorting and filtering

## Extended Usage Examples

### Basic Search Patterns
```
# Search by model name
GET /api/handbags/search?modelName=luxury&page=1&pageSize=10

# Search by material
GET /api/handbags/search?material=leather&page=1&pageSize=15

# Search by color
GET /api/handbags/search?color=black&sortBy=price&sortOrder=asc

# Search by brand
GET /api/handbags/search?brandName=gucci&page=1&pageSize=20
```

### Price Range Searches
```
# Budget range ($50-$200)
GET /api/handbags/search?minPrice=50&maxPrice=200&page=1&pageSize=10&sortBy=price&sortOrder=asc

# Luxury range ($1000-$5000)
GET /api/handbags/search?minPrice=1000&maxPrice=5000&sortBy=price&sortOrder=desc

# Under budget (under $500)
GET /api/handbags/search?maxPrice=500&sortBy=price&sortOrder=asc

# Premium only (over $2000)
GET /api/handbags/search?minPrice=2000&sortBy=releasedate&sortOrder=desc
```

### Stock Management
```
# Low stock items (≤5 items)
GET /api/handbags/search?maxStock=5&sortBy=stock&sortOrder=asc

# In stock items (≥1 item)
GET /api/handbags/search?minStock=1&sortBy=stock&sortOrder=desc

# High stock items (≥50 items)
GET /api/handbags/search?minStock=50&sortBy=modelname&sortOrder=asc

# Stock range (10-100 items)
GET /api/handbags/search?minStock=10&maxStock=100&page=1&pageSize=25
```

### Date-Based Searches
```
# New releases (from 2024)
GET /api/handbags/search?fromDate=2024-01-01&sortBy=releasedate&sortOrder=desc

# This year only
GET /api/handbags/search?fromDate=2024-01-01&toDate=2024-12-31&page=1&pageSize=20

# Vintage items (before 2021)
GET /api/handbags/search?toDate=2020-12-31&sortBy=releasedate&sortOrder=asc

# Recent releases (last year)
GET /api/handbags/search?fromDate=2023-06-01&toDate=2024-06-01&sortBy=releasedate&sortOrder=desc
```

### Complex Multi-Filter Searches
```
# Luxury leather bags ($500-$3000)
GET /api/handbags/search?material=leather&minPrice=500&maxPrice=3000&sortBy=price&sortOrder=desc

# Designer black bags (over $300)
GET /api/handbags/search?color=black&brandName=designer&minPrice=300&page=1&pageSize=15

# New luxury collection (over $1000, from 2024)
GET /api/handbags/search?minPrice=1000&fromDate=2024-01-01&sortBy=price&sortOrder=desc&page=1&pageSize=10

# Affordable leather bags (under $400, in stock)
GET /api/handbags/search?material=leather&maxPrice=400&minStock=5&sortBy=price&sortOrder=asc
```

### Grouped Search Examples
```
# All bags grouped by brand
GET /api/handbags/search/grouped?page=1&pageSize=50

# Luxury bags grouped by brand
GET /api/handbags/search/grouped?minPrice=500&page=1&pageSize=30

# Leather bags grouped by brand
GET /api/handbags/search/grouped?material=leather&sortBy=price&sortOrder=desc

# New releases grouped by brand
GET /api/handbags/search/grouped?fromDate=2024-01-01&sortBy=releasedate&sortOrder=desc
```

### Sorting Variations
```
# Sort by newest first
GET /api/handbags/search?sortBy=releasedate&sortOrder=desc&page=1&pageSize=20

# Sort by price (high to low)
GET /api/handbags/search?sortBy=price&sortOrder=desc&page=1&pageSize=15

# Sort by brand name
GET /api/handbags/search?sortBy=brandname&sortOrder=asc&page=1&pageSize=25

# Sort by model name
GET /api/handbags/search?sortBy=modelname&sortOrder=asc&page=1&pageSize=30
```

### Special Use Cases
```
# Inventory check (out of stock items)
GET /api/handbags/search?minStock=0&maxStock=0&sortBy=modelname&sortOrder=asc

# Top sellers (high stock items)
GET /api/handbags/search?minStock=100&sortBy=stock&sortOrder=desc&page=1&pageSize=20

# Clearance items (low price, low stock)
GET /api/handbags/search?maxPrice=100&maxStock=10&sortBy=price&sortOrder=asc

# New arrivals (recent releases)
GET /api/handbags/search?fromDate=2024-11-01&sortBy=releasedate&sortOrder=desc&page=1&pageSize=15
```

## Usage Tips
1. Use pagination for better performance with large datasets
2. Combine multiple filters for precise searches
3. Use the `/search/options` endpoint to discover available filters
4. Monitor search duration in metadata for performance optimization
5. Use grouped search for brand-based analysis
6. Combine price and date filters for seasonal promotions
7. Use stock filters for inventory management
8. Test complex queries with small page sizes first
