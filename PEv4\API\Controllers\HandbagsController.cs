﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Model;
using Services;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.OData.Query.Validator;
using System.Net;
using Helper;

namespace PRN231_SU25_SE170115.api.Controllers
{
    [ApiController]
    [Route("api/handbags")]
    [Authorize]
    public class HandbagsController : ControllerBase
    {
        private readonly HangBagService _handbagService;

        public HandbagsController(HangBagService handbagService)
        {
            _handbagService = handbagService;
        }

        [HttpGet]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public async Task<IActionResult> GetAllHandbags([FromQuery] PaginationRequest request)
        {
            try
            {
                var result = await _handbagService.GetPaginatedHandbagsAsync(request);
                var response = ApiResponse<PaginatedResponse<ListHandBagModel>>.SuccessResult(result, "Handbags retrieved successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<PaginatedResponse<ListHandBagModel>>.ErrorResult("Failed to retrieve handbags", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetHandbagById(int id)
        {
            try
            {
                var handbag = _handbagService.GetHandBagById(id);
                if (handbag == null)
                {
                    var notFoundResponse = ApiResponse<ListHandBagModel>.ErrorResult("Handbag not found");
                    return NotFound(notFoundResponse);
                }

                var response = ApiResponse<ListHandBagModel>.SuccessResult(handbag, "Handbag retrieved successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<ListHandBagModel>.ErrorResult("Failed to retrieve handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpPost]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult CreateHandbag([FromBody] CreateHandBagModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var validationResponse = ApiResponse<object>.ErrorResult("Validation failed", ModelState);
                    return BadRequest(validationResponse);
                }

                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName))
                {
                    var formatResponse = ApiResponse<object>.ErrorResult("Invalid model name format");
                    return BadRequest(formatResponse);
                }

                if (model.Price <= 0 || model.Stock <= 0)
                {
                    var valueResponse = ApiResponse<object>.ErrorResult("Price and stock must be greater than 0");
                    return BadRequest(valueResponse);
                }

                var result = _handbagService.CreateHandBag(model);
                if (result == null)
                {
                    var createResponse = ApiResponse<object>.ErrorResult("Failed to create handbag");
                    return BadRequest(createResponse);
                }

                var response = ApiResponse<object>.SuccessResult(result, "Handbag created successfully");
                return StatusCode(201, response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to create handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult UpdateHandbag(int id, [FromBody] UpdateHandBagModel model)
        {
            try
            {
                if (id <= 0)
                {
                    var idResponse = ApiResponse<object>.ErrorResult("Invalid handbag ID");
                    return BadRequest(idResponse);
                }

                var item = _handbagService.GetHandBagById(id);
                if (item == null)
                {
                    var notFoundResponse = ApiResponse<object>.ErrorResult("Handbag not found");
                    return NotFound(notFoundResponse);
                }

                if (!ModelState.IsValid)
                {
                    var validationResponse = ApiResponse<object>.ErrorResult("Validation failed", ModelState);
                    return BadRequest(validationResponse);
                }

                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName))
                {
                    var formatResponse = ApiResponse<object>.ErrorResult("Invalid model name format");
                    return BadRequest(formatResponse);
                }

                if (model.Price <= 0 || model.Stock <= 0)
                {
                    var valueResponse = ApiResponse<object>.ErrorResult("Price and stock must be greater than 0");
                    return BadRequest(valueResponse);
                }

                var result = _handbagService.UpdateHandBag(id, model);
                if (!result)
                {
                    var updateResponse = ApiResponse<object>.ErrorResult("Failed to update handbag");
                    return BadRequest(updateResponse);
                }

                var response = ApiResponse<object>.SuccessResult(null, "Handbag updated successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to update handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult DeleteHandbag(int id)
        {
            try
            {
                if (id <= 0)
                {
                    var idResponse = ApiResponse<object>.ErrorResult("Invalid handbag ID");
                    return BadRequest(idResponse);
                }

                var result = _handbagService.DeleteHandBag(id);
                if (!result)
                {
                    var notFoundResponse = ApiResponse<object>.ErrorResult("Handbag not found");
                    return NotFound(notFoundResponse);
                }

                var response = ApiResponse<object>.SuccessResult(null, "Handbag deleted successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to delete handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [Authorize(Roles = "administrator,moderator,developer,member")]
        [HttpGet("search")]
        public async Task<IActionResult> SearchHandbags([FromQuery] HandbagSearchRequest request)
        {
            try
            {
                var result = await _handbagService.SearchHandbagsAsync(request);
                var response = ApiResponse<SearchResponse<ListHandBagModel>>.SuccessResult(result, "Search completed successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<SearchResponse<ListHandBagModel>>.ErrorResult("Search failed", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [Authorize(Roles = "administrator,moderator,developer,member")]
        [HttpGet("search/grouped")]
        public async Task<IActionResult> SearchHandbagsGrouped([FromQuery] HandbagSearchRequest request)
        {
            try
            {
                var searchResult = await _handbagService.SearchHandbagsAsync(request);

                var grouped = searchResult.Items
                    .GroupBy(h => h.BrandName)
                    .Select(g => new GroupedHandbagModel
                    {
                        BrandName = g.Key,
                        Handbags = g.ToList()
                    })
                    .ToList();

                var groupedResponse = new
                {
                    Groups = grouped,
                    TotalCount = searchResult.TotalCount,
                    Page = searchResult.Page,
                    PageSize = searchResult.PageSize,
                    TotalPages = searchResult.TotalPages,
                    Metadata = searchResult.Metadata
                };

                var response = ApiResponse<object>.SuccessResult(groupedResponse, "Grouped search completed successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Grouped search failed", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpGet("search/options")]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetSearchOptions()
        {
            try
            {
                var options = new
                {
                    SortableFields = new[]
                    {
                        "handbagid", "modelname", "material", "color", "price",
                        "stock", "releasedate", "brandname", "country", "foundedyear"
                    },
                    SortOrders = new[] { "asc", "desc" },
                    FilterableFields = new
                    {
                        ModelName = "string - partial match",
                        Material = "string - partial match",
                        Color = "string - partial match",
                        BrandName = "string - partial match",
                        MinPrice = "decimal - minimum price filter",
                        MaxPrice = "decimal - maximum price filter",
                        MinStock = "int - minimum stock filter",
                        MaxStock = "int - maximum stock filter",
                        FromDate = "DateOnly - from release date",
                        ToDate = "DateOnly - to release date"
                    },
                    PaginationDefaults = new
                    {
                        Page = 1,
                        PageSize = 10,
                        MaxPageSize = 100
                    },
                    ExampleRequests = new
                    {
                        BasicPagination = "/api/handbags?page=1&pageSize=10",
                        WithSorting = "/api/handbags?page=1&pageSize=10&sortBy=price&sortOrder=desc",
                        Search = "/api/handbags/search?modelName=luxury&minPrice=100&maxPrice=1000&page=1&pageSize=10&sortBy=price&sortOrder=asc",
                        GroupedSearch = "/api/handbags/search/grouped?brandName=gucci&page=1&pageSize=20"
                    }
                };

                var response = ApiResponse<object>.SuccessResult(options, "Search options retrieved successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to retrieve search options", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }
    }
}
