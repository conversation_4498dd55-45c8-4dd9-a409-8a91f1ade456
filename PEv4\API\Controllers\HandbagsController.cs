﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Model;
using Services;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.OData.Query.Validator;
using System.Net;
using Helper;

namespace PRN231_SU25_SE170115.api.Controllers
{
    [ApiController]
    [Route("api/handbags")]
    [Authorize]
    public class HandbagsController : ControllerBase
    {
        private readonly HangBagService _handbagService;

        public HandbagsController(HangBagService handbagService)
        {
            _handbagService = handbagService;
        }

        [HttpGet]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public async Task<IActionResult> GetAllHandbags([FromQuery] PaginationRequest request)
        {
            try
            {
                var result = await _handbagService.GetPaginatedHandbagsAsync(request);
                var response = ApiResponse<PaginatedResponse<ListHandBagModel>>.SuccessResult(result, "Handbags retrieved successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<PaginatedResponse<ListHandBagModel>>.ErrorResult("Failed to retrieve handbags", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetHandbagById(int id)
        {
            try
            {
                var handbag = _handbagService.GetHandBagById(id);
                if (handbag == null)
                {
                    var notFoundResponse = ApiResponse<ListHandBagModel>.ErrorResult("Handbag not found");
                    return NotFound(notFoundResponse);
                }

                var response = ApiResponse<ListHandBagModel>.SuccessResult(handbag, "Handbag retrieved successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<ListHandBagModel>.ErrorResult("Failed to retrieve handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpPost]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult CreateHandbag([FromBody] CreateHandBagModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var validationResponse = ApiResponse<object>.ErrorResult("Validation failed", ModelState);
                    return BadRequest(validationResponse);
                }

                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName))
                {
                    var formatResponse = ApiResponse<object>.ErrorResult("Invalid model name format");
                    return BadRequest(formatResponse);
                }

                if (model.Price <= 0 || model.Stock <= 0)
                {
                    var valueResponse = ApiResponse<object>.ErrorResult("Price and stock must be greater than 0");
                    return BadRequest(valueResponse);
                }

                var result = _handbagService.CreateHandBag(model);
                if (result == null)
                {
                    var createResponse = ApiResponse<object>.ErrorResult("Failed to create handbag");
                    return BadRequest(createResponse);
                }

                var response = ApiResponse<object>.SuccessResult(result, "Handbag created successfully");
                return StatusCode(201, response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to create handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult UpdateHandbag(int id, [FromBody] UpdateHandBagModel model)
        {
            try
            {
                if (id <= 0)
                {
                    var idResponse = ApiResponse<object>.ErrorResult("Invalid handbag ID");
                    return BadRequest(idResponse);
                }

                var item = _handbagService.GetHandBagById(id);
                if (item == null)
                {
                    var notFoundResponse = ApiResponse<object>.ErrorResult("Handbag not found");
                    return NotFound(notFoundResponse);
                }

                if (!ModelState.IsValid)
                {
                    var validationResponse = ApiResponse<object>.ErrorResult("Validation failed", ModelState);
                    return BadRequest(validationResponse);
                }

                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName))
                {
                    var formatResponse = ApiResponse<object>.ErrorResult("Invalid model name format");
                    return BadRequest(formatResponse);
                }

                if (model.Price <= 0 || model.Stock <= 0)
                {
                    var valueResponse = ApiResponse<object>.ErrorResult("Price and stock must be greater than 0");
                    return BadRequest(valueResponse);
                }

                var result = _handbagService.UpdateHandBag(id, model);
                if (!result)
                {
                    var updateResponse = ApiResponse<object>.ErrorResult("Failed to update handbag");
                    return BadRequest(updateResponse);
                }

                var response = ApiResponse<object>.SuccessResult(null, "Handbag updated successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to update handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult DeleteHandbag(int id)
        {
            try
            {
                if (id <= 0)
                {
                    var idResponse = ApiResponse<object>.ErrorResult("Invalid handbag ID");
                    return BadRequest(idResponse);
                }

                var result = _handbagService.DeleteHandBag(id);
                if (!result)
                {
                    var notFoundResponse = ApiResponse<object>.ErrorResult("Handbag not found");
                    return NotFound(notFoundResponse);
                }

                var response = ApiResponse<object>.SuccessResult(null, "Handbag deleted successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to delete handbag", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [Authorize(Roles = "administrator,moderator,developer,member")]
        [HttpGet("search")]
        public async Task<IActionResult> SearchHandbags([FromQuery] HandbagSearchRequest request)
        {
            try
            {
                var result = await _handbagService.SearchHandbagsAsync(request);
                var response = ApiResponse<SearchResponse<ListHandBagModel>>.SuccessResult(result, "Search completed successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<SearchResponse<ListHandBagModel>>.ErrorResult("Search failed", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [Authorize(Roles = "administrator,moderator,developer,member")]
        [HttpGet("search/grouped")]
        public async Task<IActionResult> SearchHandbagsGrouped([FromQuery] HandbagSearchRequest request)
        {
            try
            {
                var searchResult = await _handbagService.SearchHandbagsAsync(request);

                var grouped = searchResult.Items
                    .GroupBy(h => h.BrandName)
                    .Select(g => new GroupedHandbagModel
                    {
                        BrandName = g.Key,
                        Handbags = g.ToList()
                    })
                    .ToList();

                var groupedResponse = new
                {
                    Groups = grouped,
                    TotalCount = searchResult.TotalCount,
                    Page = searchResult.Page,
                    PageSize = searchResult.PageSize,
                    TotalPages = searchResult.TotalPages,
                    Metadata = searchResult.Metadata
                };

                var response = ApiResponse<object>.SuccessResult(groupedResponse, "Grouped search completed successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Grouped search failed", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }

        [HttpGet("search/options")]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetSearchOptions()
        {
            try
            {
                var options = new
                {
                    SortableFields = new[]
                    {
                        "handbagid", "modelname", "material", "color", "price",
                        "stock", "releasedate", "brandname", "country", "foundedyear"
                    },
                    SortOrders = new[] { "asc", "desc" },
                    FilterableFields = new
                    {
                        ModelName = "string - partial match",
                        Material = "string - partial match",
                        Color = "string - partial match",
                        BrandName = "string - partial match",
                        MinPrice = "decimal - minimum price filter",
                        MaxPrice = "decimal - maximum price filter",
                        MinStock = "int - minimum stock filter",
                        MaxStock = "int - maximum stock filter",
                        FromDate = "DateOnly - from release date",
                        ToDate = "DateOnly - to release date"
                    },
                    PaginationDefaults = new
                    {
                        Page = 1,
                        PageSize = 10,
                        MaxPageSize = 100
                    },
                    ExampleRequests = new
                    {
                        // Basic Operations
                        BasicPagination = "/api/handbags?page=1&pageSize=10",
                        WithSorting = "/api/handbags?page=1&pageSize=10&sortBy=price&sortOrder=desc",
                        GetById = "/api/handbags/123",

                        // Simple Search Examples
                        SearchByModelName = "/api/handbags/search?modelName=luxury&page=1&pageSize=10",
                        SearchByMaterial = "/api/handbags/search?material=leather&page=1&pageSize=15",
                        SearchByColor = "/api/handbags/search?color=black&sortBy=price&sortOrder=asc",
                        SearchByBrand = "/api/handbags/search?brandName=gucci&page=1&pageSize=20",

                        // Price Range Examples
                        BudgetRange = "/api/handbags/search?minPrice=50&maxPrice=200&page=1&pageSize=10&sortBy=price&sortOrder=asc",
                        LuxuryRange = "/api/handbags/search?minPrice=1000&maxPrice=5000&sortBy=price&sortOrder=desc",
                        UnderBudget = "/api/handbags/search?maxPrice=500&sortBy=price&sortOrder=asc",
                        PremiumOnly = "/api/handbags/search?minPrice=2000&sortBy=releasedate&sortOrder=desc",

                        // Stock Management Examples
                        LowStock = "/api/handbags/search?maxStock=5&sortBy=stock&sortOrder=asc",
                        InStock = "/api/handbags/search?minStock=1&sortBy=stock&sortOrder=desc",
                        HighStock = "/api/handbags/search?minStock=50&sortBy=modelname&sortOrder=asc",
                        StockRange = "/api/handbags/search?minStock=10&maxStock=100&page=1&pageSize=25",

                        // Date Range Examples
                        NewReleases = "/api/handbags/search?fromDate=2024-01-01&sortBy=releasedate&sortOrder=desc",
                        ThisYear = "/api/handbags/search?fromDate=2024-01-01&toDate=2024-12-31&page=1&pageSize=20",
                        Vintage = "/api/handbags/search?toDate=2020-12-31&sortBy=releasedate&sortOrder=asc",
                        RecentReleases = "/api/handbags/search?fromDate=2023-06-01&toDate=2024-06-01&sortBy=releasedate&sortOrder=desc",

                        // Complex Multi-Filter Examples
                        LuxuryLeatherBags = "/api/handbags/search?material=leather&minPrice=500&maxPrice=3000&sortBy=price&sortOrder=desc",
                        DesignerBlackBags = "/api/handbags/search?color=black&brandName=designer&minPrice=300&page=1&pageSize=15",
                        NewLuxuryCollection = "/api/handbags/search?minPrice=1000&fromDate=2024-01-01&sortBy=price&sortOrder=desc&page=1&pageSize=10",
                        AffordableLeatherBags = "/api/handbags/search?material=leather&maxPrice=400&minStock=5&sortBy=price&sortOrder=asc",

                        // Brand-Specific Examples
                        GucciBags = "/api/handbags/search?brandName=gucci&sortBy=price&sortOrder=desc&page=1&pageSize=20",
                        ItalianBrands = "/api/handbags/search?brandName=italian&minPrice=200&sortBy=foundedyear&sortOrder=asc",
                        EstablishedBrands = "/api/handbags/search?brandName=luxury&sortBy=foundedyear&sortOrder=asc",

                        // Sorting Examples
                        SortByNewest = "/api/handbags/search?sortBy=releasedate&sortOrder=desc&page=1&pageSize=20",
                        SortByOldest = "/api/handbags/search?sortBy=releasedate&sortOrder=asc&page=1&pageSize=20",
                        SortByPriceHigh = "/api/handbags/search?sortBy=price&sortOrder=desc&page=1&pageSize=15",
                        SortByPriceLow = "/api/handbags/search?sortBy=price&sortOrder=asc&page=1&pageSize=15",
                        SortByBrand = "/api/handbags/search?sortBy=brandname&sortOrder=asc&page=1&pageSize=25",
                        SortByModel = "/api/handbags/search?sortBy=modelname&sortOrder=asc&page=1&pageSize=30",

                        // Grouped Search Examples
                        GroupedByBrand = "/api/handbags/search/grouped?page=1&pageSize=50",
                        GroupedLuxuryBags = "/api/handbags/search/grouped?minPrice=500&page=1&pageSize=30",
                        GroupedLeatherBags = "/api/handbags/search/grouped?material=leather&sortBy=price&sortOrder=desc",
                        GroupedNewReleases = "/api/handbags/search/grouped?fromDate=2024-01-01&sortBy=releasedate&sortOrder=desc",
                        GroupedByPriceRange = "/api/handbags/search/grouped?minPrice=200&maxPrice=1000&page=1&pageSize=40",

                        // Pagination Examples
                        FirstPage = "/api/handbags/search?page=1&pageSize=10",
                        SecondPage = "/api/handbags/search?page=2&pageSize=10",
                        LargePageSize = "/api/handbags/search?page=1&pageSize=50",
                        SmallPageSize = "/api/handbags/search?page=1&pageSize=5",

                        // Performance Testing Examples
                        LargeDataset = "/api/handbags/search?page=1&pageSize=100&sortBy=handbagid&sortOrder=asc",
                        ComplexQuery = "/api/handbags/search?modelName=designer&material=leather&color=black&minPrice=300&maxPrice=2000&minStock=1&fromDate=2023-01-01&page=1&pageSize=25&sortBy=price&sortOrder=desc",

                        // Edge Cases
                        EmptySearch = "/api/handbags/search?page=1&pageSize=10",
                        SingleResult = "/api/handbags/search?modelName=uniquemodel&page=1&pageSize=1",
                        NoResults = "/api/handbags/search?modelName=nonexistent&page=1&pageSize=10",

                        // Special Use Cases
                        InventoryCheck = "/api/handbags/search?minStock=0&maxStock=0&sortBy=modelname&sortOrder=asc",
                        TopSellers = "/api/handbags/search?minStock=100&sortBy=stock&sortOrder=desc&page=1&pageSize=20",
                        ClearanceItems = "/api/handbags/search?maxPrice=100&maxStock=10&sortBy=price&sortOrder=asc",
                        NewArrivals = "/api/handbags/search?fromDate=2024-11-01&sortBy=releasedate&sortOrder=desc&page=1&pageSize=15"
                    }
                };

                var response = ApiResponse<object>.SuccessResult(options, "Search options retrieved successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                var errorResponse = ApiResponse<object>.ErrorResult("Failed to retrieve search options", ex.Message);
                return StatusCode(500, errorResponse);
            }
        }
    }
}
