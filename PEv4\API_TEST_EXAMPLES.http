### API Test Examples for Handbags API

### Variables
@baseUrl = https://localhost:7000/api
@token = your_jwt_token_here

### 1. Get Search Options
GET {{baseUrl}}/handbags/search/options
Authorization: Bearer {{token}}

### 2. Get All Handbags (Basic Pagination)
GET {{baseUrl}}/handbags?page=1&pageSize=10
Authorization: Bearer {{token}}

### 3. Get All Handbags with Sorting
GET {{baseUrl}}/handbags?page=1&pageSize=10&sortBy=price&sortOrder=desc
Authorization: Bearer {{token}}

### 4. Get Handbag by ID
GET {{baseUrl}}/handbags/1
Authorization: Bearer {{token}}

### 5. Advanced Search - Basic
GET {{baseUrl}}/handbags/search?modelName=luxury&page=1&pageSize=10
Authorization: Bearer {{token}}

### 6. Advanced Search - Price Range
GET {{baseUrl}}/handbags/search?minPrice=100&maxPrice=1000&page=1&pageSize=10&sortBy=price&sortOrder=asc
Authorization: Bearer {{token}}

### 7. Advanced Search - Multiple Filters
GET {{baseUrl}}/handbags/search?modelName=bag&material=leather&brandName=gucci&minPrice=200&maxPrice=2000&page=1&pageSize=20&sortBy=price&sortOrder=desc
Authorization: Bearer {{token}}

### 8. Advanced Search - Date Range
GET {{baseUrl}}/handbags/search?fromDate=2024-01-01&toDate=2024-12-31&page=1&pageSize=10&sortBy=releasedate&sortOrder=desc
Authorization: Bearer {{token}}

### 9. Advanced Search - Stock Range
GET {{baseUrl}}/handbags/search?minStock=5&maxStock=50&page=1&pageSize=15&sortBy=stock&sortOrder=asc
Authorization: Bearer {{token}}

### 10. Grouped Search
GET {{baseUrl}}/handbags/search/grouped?brandName=gucci&page=1&pageSize=20
Authorization: Bearer {{token}}

### 11. Grouped Search with Multiple Filters
GET {{baseUrl}}/handbags/search/grouped?material=leather&minPrice=300&page=1&pageSize=10&sortBy=price&sortOrder=desc
Authorization: Bearer {{token}}

### 12. Create New Handbag
POST {{baseUrl}}/handbags
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "modelName": "Luxury Evening Bag",
  "material": "Premium Leather",
  "price": 599.99,
  "stock": 15,
  "brandId": 1
}

### 13. Update Handbag
PUT {{baseUrl}}/handbags/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "modelName": "Updated Luxury Bag",
  "material": "Italian Leather",
  "color": "Burgundy",
  "price": 649.99,
  "stock": 12,
  "brandId": 1
}

### 14. Delete Handbag
DELETE {{baseUrl}}/handbags/1
Authorization: Bearer {{token}}

### 15. Test Error Cases

# Invalid ID
GET {{baseUrl}}/handbags/999999
Authorization: Bearer {{token}}

# Invalid pagination
GET {{baseUrl}}/handbags?page=0&pageSize=-1
Authorization: Bearer {{token}}

# Invalid sort field
GET {{baseUrl}}/handbags?sortBy=invalidfield&sortOrder=asc
Authorization: Bearer {{token}}

### 16. Performance Test - Large Page Size
GET {{baseUrl}}/handbags/search?page=1&pageSize=100&sortBy=handbagid&sortOrder=asc
Authorization: Bearer {{token}}

### 17. Complex Search Query
GET {{baseUrl}}/handbags/search?modelName=designer&material=leather&color=black&brandName=luxury&minPrice=500&maxPrice=5000&minStock=1&maxStock=100&fromDate=2023-01-01&toDate=2024-12-31&page=1&pageSize=25&sortBy=price&sortOrder=desc
Authorization: Bearer {{token}}

### 18. Search with No Results
GET {{baseUrl}}/handbags/search?modelName=nonexistentmodel&page=1&pageSize=10
Authorization: Bearer {{token}}

### 19. Test Different Sort Orders
# Sort by model name ascending
GET {{baseUrl}}/handbags?sortBy=modelname&sortOrder=asc&page=1&pageSize=10
Authorization: Bearer {{token}}

# Sort by release date descending
GET {{baseUrl}}/handbags?sortBy=releasedate&sortOrder=desc&page=1&pageSize=10
Authorization: Bearer {{token}}

# Sort by brand name ascending
GET {{baseUrl}}/handbags?sortBy=brandname&sortOrder=asc&page=1&pageSize=10
Authorization: Bearer {{token}}

### 20. Test Edge Cases
# Minimum page size
GET {{baseUrl}}/handbags?page=1&pageSize=1
Authorization: Bearer {{token}}

# Large page number
GET {{baseUrl}}/handbags?page=1000&pageSize=10
Authorization: Bearer {{token}}

# Price filter edge cases
GET {{baseUrl}}/handbags/search?minPrice=0.01&maxPrice=999999.99
Authorization: Bearer {{token}}

### Notes:
# 1. Replace {{token}} with your actual JWT token
# 2. Replace {{baseUrl}} with your actual API base URL
# 3. Adjust handbag IDs and brand IDs based on your test data
# 4. Some endpoints require specific roles (administrator, moderator)
# 5. Monitor response times and search duration in metadata
