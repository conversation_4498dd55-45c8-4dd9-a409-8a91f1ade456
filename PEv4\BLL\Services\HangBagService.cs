﻿
using DAL.Entities;
using Microsoft.EntityFrameworkCore;
using Model;
using UOW;
using System.Linq.Expressions;
using System.Diagnostics;

namespace Services
{
    public class HangBagService 
    {
        private readonly UnitOfWork _unitOfWork;

        public HangBagService(UnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public List<ListHandBagModel> GetAllHandBag()
        {
            var list = _unitOfWork.GetRepository<Handbag>()
                .Entities
                .Select(x => new ListHandBagModel
                {
                    HandbagId = x.HandbagId,
                    ModelName = x.ModelName,
                    Material = x.Material,
                    Color = x.Color,
                    Price = x.Price,
                    ReleaseDate = x.ReleaseDate,
                    Stock = x.Stock,
                    BrandName = x.Brand.BrandName,
                    Country = x.Brand.Country,
                    FoundedYear = x.Brand.FoundedYear,
                    Website = x.Brand.Website,
                })
                .ToList();

            return list;
        }

        public ListHandBagModel GetHandBagById(int id)
        {
            var handbag = _unitOfWork.GetRepository<Handbag>()
                .Entities
                .Select(x => new ListHandBagModel
                {
                    HandbagId = x.HandbagId,
                    ModelName = x.ModelName,
                    Material = x.Material,
                    Color = x.Color,
                    Price = x.Price,
                    ReleaseDate = x.ReleaseDate,
                    Stock = x.Stock,
                    BrandName = x.Brand.BrandName,
                    Country = x.Brand.Country,
                    FoundedYear = x.Brand.FoundedYear,
                    Website = x.Brand.Website,
                })
                .FirstOrDefault(x => x.HandbagId == id);

            return handbag; // Return null if not found - Controller will handle
        }

        public Handbag CreateHandBag(CreateHandBagModel model)
        {
            try
            {

           
            var maxId = _unitOfWork.GetRepository<Handbag>().Entities.Max(h => (int?)h.HandbagId) ?? 0;
            var handbag = new Handbag
            {
                HandbagId = maxId+1,
                ModelName = model.ModelName,
                Material = model.Material,
                Price = model.Price,
                Stock = model.Stock,
                BrandId = model.BrandId,
                ReleaseDate = DateOnly.FromDateTime(DateTime.Now)
            };
            _unitOfWork.GetRepository<Handbag>().Add(handbag);
            _unitOfWork.Save();
            return handbag;
            }
            catch(Exception ex)
            {
                return null;
            }
        }

        public bool UpdateHandBag(int id, UpdateHandBagModel model)
        {
            var handbag = _unitOfWork.GetRepository<Handbag>().GetById(id);
            if (handbag == null)
                return false; // Return false if not found - Controller will handle

            handbag.Material = model.Material;
            handbag.Price = model.Price;
            handbag.Stock = model.Stock;
            handbag.Color = model.Color;
            handbag.ModelName = model.ModelName;
            handbag.BrandId = model.BrandId;
            _unitOfWork.GetRepository<Handbag>().Update(handbag);
            _unitOfWork.Save();

            return true;
        }

        public bool DeleteHandBag(int id)
        {
            var handbag = _unitOfWork.GetRepository<Handbag>().GetById(id);
            if(handbag == null)
                return false; // Return false if not found - Controller will handle

            _unitOfWork.GetRepository<Handbag>().Delete(handbag);
            _unitOfWork.Save();

            return true;
        }

        public IQueryable<ListHandBagModel> SearchWithProjection(string? modelName, string? material)
        {
            var query = _unitOfWork.GetRepository<Handbag>().Entities
                .Include(h => h.Brand)
                .Select(h => new ListHandBagModel
                {
                    HandbagId = h.HandbagId,
                    ModelName = h.ModelName,
                    Material = h.Material,
                    Color = h.Color,
                    Price = h.Price,
                    Stock = h.Stock,
                    ReleaseDate = h.ReleaseDate,
                    BrandName = h.Brand.BrandName,
                    Country = h.Brand.Country,
                    FoundedYear = h.Brand.FoundedYear,
                    Website = h.Brand.Website
                });

            if (!string.IsNullOrEmpty(modelName))
            {
                query = query.Where(h => h.ModelName.Contains(modelName));
            }

            if (!string.IsNullOrEmpty(material))
            {
                query = query.Where(h => h.Material.Contains(material));
            }
            return query;
        }

        // Advanced search with pagination and sorting
        public async Task<SearchResponse<ListHandBagModel>> SearchHandbagsAsync(HandbagSearchRequest request)
        {
            var stopwatch = Stopwatch.StartNew();

            var query = _unitOfWork.GetRepository<Handbag>().Entities
                .Include(h => h.Brand)
                .Select(h => new ListHandBagModel
                {
                    HandbagId = h.HandbagId,
                    ModelName = h.ModelName,
                    Material = h.Material,
                    Color = h.Color,
                    Price = h.Price,
                    Stock = h.Stock,
                    ReleaseDate = h.ReleaseDate,
                    BrandName = h.Brand.BrandName,
                    Country = h.Brand.Country,
                    FoundedYear = h.Brand.FoundedYear,
                    Website = h.Brand.Website
                });

            // Apply filters
            var appliedFilters = new Dictionary<string, object>();

            if (!string.IsNullOrEmpty(request.ModelName))
            {
                query = query.Where(h => h.ModelName.Contains(request.ModelName));
                appliedFilters["ModelName"] = request.ModelName;
            }

            if (!string.IsNullOrEmpty(request.Material))
            {
                query = query.Where(h => h.Material.Contains(request.Material));
                appliedFilters["Material"] = request.Material;
            }

            if (!string.IsNullOrEmpty(request.Color))
            {
                query = query.Where(h => h.Color.Contains(request.Color));
                appliedFilters["Color"] = request.Color;
            }

            if (!string.IsNullOrEmpty(request.BrandName))
            {
                query = query.Where(h => h.BrandName.Contains(request.BrandName));
                appliedFilters["BrandName"] = request.BrandName;
            }

            if (request.MinPrice.HasValue)
            {
                query = query.Where(h => h.Price >= request.MinPrice.Value);
                appliedFilters["MinPrice"] = request.MinPrice.Value;
            }

            if (request.MaxPrice.HasValue)
            {
                query = query.Where(h => h.Price <= request.MaxPrice.Value);
                appliedFilters["MaxPrice"] = request.MaxPrice.Value;
            }

            if (request.MinStock.HasValue)
            {
                query = query.Where(h => h.Stock >= request.MinStock.Value);
                appliedFilters["MinStock"] = request.MinStock.Value;
            }

            if (request.MaxStock.HasValue)
            {
                query = query.Where(h => h.Stock <= request.MaxStock.Value);
                appliedFilters["MaxStock"] = request.MaxStock.Value;
            }

            if (request.FromDate.HasValue)
            {
                query = query.Where(h => h.ReleaseDate >= request.FromDate.Value);
                appliedFilters["FromDate"] = request.FromDate.Value;
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(h => h.ReleaseDate <= request.ToDate.Value);
                appliedFilters["ToDate"] = request.ToDate.Value;
            }

            // Apply sorting
            if (!string.IsNullOrEmpty(request.SortBy))
            {
                query = ApplySorting(query, request.SortBy, request.SortOrder);
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var items = await query
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            stopwatch.Stop();

            var metadata = new SearchMetadata
            {
                AppliedFilters = appliedFilters,
                SortBy = request.SortBy,
                SortOrder = request.SortOrder,
                ResultCount = totalCount,
                SearchDurationMs = stopwatch.Elapsed.TotalMilliseconds
            };

            return new SearchResponse<ListHandBagModel>(items, totalCount, request.Page, request.PageSize, metadata);
        }

        // Get paginated handbags without search filters
        public async Task<PaginatedResponse<ListHandBagModel>> GetPaginatedHandbagsAsync(PaginationRequest request)
        {
            var query = _unitOfWork.GetRepository<Handbag>().Entities
                .Include(h => h.Brand)
                .Select(h => new ListHandBagModel
                {
                    HandbagId = h.HandbagId,
                    ModelName = h.ModelName,
                    Material = h.Material,
                    Color = h.Color,
                    Price = h.Price,
                    Stock = h.Stock,
                    ReleaseDate = h.ReleaseDate,
                    BrandName = h.Brand.BrandName,
                    Country = h.Brand.Country,
                    FoundedYear = h.Brand.FoundedYear,
                    Website = h.Brand.Website
                });

            // Apply sorting
            if (!string.IsNullOrEmpty(request.SortBy))
            {
                query = ApplySorting(query, request.SortBy, request.SortOrder);
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var items = await query
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            return new PaginatedResponse<ListHandBagModel>(items, totalCount, request.Page, request.PageSize);
        }

        private IQueryable<ListHandBagModel> ApplySorting(IQueryable<ListHandBagModel> query, string sortBy, string? sortOrder)
        {
            var isDescending = sortOrder?.ToLower() == "desc";

            return sortBy.ToLower() switch
            {
                "handbagid" => isDescending ? query.OrderByDescending(h => h.HandbagId) : query.OrderBy(h => h.HandbagId),
                "modelname" => isDescending ? query.OrderByDescending(h => h.ModelName) : query.OrderBy(h => h.ModelName),
                "material" => isDescending ? query.OrderByDescending(h => h.Material) : query.OrderBy(h => h.Material),
                "color" => isDescending ? query.OrderByDescending(h => h.Color) : query.OrderBy(h => h.Color),
                "price" => isDescending ? query.OrderByDescending(h => h.Price) : query.OrderBy(h => h.Price),
                "stock" => isDescending ? query.OrderByDescending(h => h.Stock) : query.OrderBy(h => h.Stock),
                "releasedate" => isDescending ? query.OrderByDescending(h => h.ReleaseDate) : query.OrderBy(h => h.ReleaseDate),
                "brandname" => isDescending ? query.OrderByDescending(h => h.BrandName) : query.OrderBy(h => h.BrandName),
                "country" => isDescending ? query.OrderByDescending(h => h.Country) : query.OrderBy(h => h.Country),
                "foundedyear" => isDescending ? query.OrderByDescending(h => h.FoundedYear) : query.OrderBy(h => h.FoundedYear),
                _ => query.OrderBy(h => h.HandbagId) // Default sorting
            };
        }

    }
}
