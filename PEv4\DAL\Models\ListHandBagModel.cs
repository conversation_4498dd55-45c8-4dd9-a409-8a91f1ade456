﻿
using System.ComponentModel.DataAnnotations;

namespace Model
{
    public class ListHandBagModel
    {
        public int HandbagId { get; set; }

        public string? ModelName { get; set; }

        public string? Material { get; set; }

        public string? Color { get; set; }

        public decimal? Price { get; set; }

        public int? Stock { get; set; }

        public DateOnly? ReleaseDate { get; set; }

        public string? BrandName { get; set; }
        public string? Country { get; set; }

        public int? FoundedYear { get; set; }

        public string? Website { get; set; }
    }

    // Pagination request model
    public class PaginationRequest
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public string? SortOrder { get; set; } = "asc"; // asc or desc
    }

    // Search request model
    public class HandbagSearchRequest : PaginationRequest
    {
        public string? ModelName { get; set; }
        public string? Material { get; set; }
        public string? Color { get; set; }
        public string? BrandName { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public int? MinStock { get; set; }
        public int? MaxStock { get; set; }
        public DateOnly? FromDate { get; set; }
        public DateOnly? ToDate { get; set; }
    }
}
