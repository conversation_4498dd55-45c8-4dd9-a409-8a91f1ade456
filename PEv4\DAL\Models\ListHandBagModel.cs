﻿
using System.ComponentModel.DataAnnotations;

namespace Model
{
    public class ListHandBagModel
    {
        public int HandbagId { get; set; }

        public string? ModelName { get; set; }

        public string? Material { get; set; }

        public string? Color { get; set; }

        public decimal? Price { get; set; }

        public int? Stock { get; set; }

        public DateOnly? ReleaseDate { get; set; }

        public string? BrandName { get; set; }
        public string? Country { get; set; }

        public int? FoundedYear { get; set; }

        public string? Website { get; set; }
    }
}
